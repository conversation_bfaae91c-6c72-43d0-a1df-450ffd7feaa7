[profile.default]
src = "src"
out = "out"
libs = ["lib"]
solc_version = "0.8.24"
optimizer = true
optimizer_runs = 200

remappings = [
  '@chainlink/contracts-ccip/=node_modules/@chainlink/contracts-ccip/',
  '@chainlink/contracts/=node_modules/@chainlink/contracts/',
]

fs_permissions = [{ access = "read-write", path = "./" }]

[etherscan]
avalanche-fuji = { key = "${ETHERSCAN_API_KEY}", chain = 43113, url = "https://api-testnet.snowtrace.io/api" }
arbitrum-sepolia = { key = "${ARBISCAN_API_KEY}", chain = 421613, url = "https://api-sepolia.arbiscan.io/api" }

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
